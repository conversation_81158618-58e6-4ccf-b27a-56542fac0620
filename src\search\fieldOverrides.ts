import { Field } from 'payload'

export const searchFields: Field[] = [
  {
    name: 'slug',
    type: 'text',
    label: {
      en: 'Slug',
      zh: '网址别名',
    },
    index: true,
    admin: {
      readOnly: true,
    },
  },
  {
    name: 'meta',
    label: {
      en: 'Meta',
      zh: '元数据',
    },
    type: 'group',
    index: true,
    admin: {
      readOnly: true,
    },
    fields: [
      {
        type: 'text',
        name: 'title',
        label: {
          en: 'Title',
          zh: '标题',
        },
      },
      {
        type: 'text',
        name: 'description',
        label: {
          en: 'Description',
          zh: '描述',
        },
      },
      {
        name: 'image',
        label: {
          en: 'Image',
          zh: '图片',
        },
        type: 'upload',
        relationTo: 'media',
      },
    ],
  },
  {
    label: {
      en: 'Categories',
      zh: '分类',
    },
    name: 'categories',
    type: 'array',
    admin: {
      readOnly: true,
    },
    fields: [
      {
        name: 'relationTo',
        type: 'text',
        label: {
          en: 'Relation To',
          zh: '关联到',
        },
      },
      {
        name: 'categoryID',
        type: 'text',
        label: {
          en: 'Category ID',
          zh: '分类ID',
        },
      },
      {
        name: 'title',
        type: 'text',
        label: {
          en: 'Title',
          zh: '标题',
        },
      },
    ],
  },
]
