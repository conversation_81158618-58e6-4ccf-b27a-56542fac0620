import { Banner } from '@payloadcms/ui/elements/Banner'
import React from 'react'

import { SeedButton } from './SeedButton'
import './index.scss'

const baseClass = 'before-dashboard'

const BeforeDashboard: React.FC = () => {
  return (
    <div className={baseClass}>
      <Banner className={`${baseClass}__banner`} type="success">
        <h4>欢迎来到您的仪表板！</h4>
      </Banner>
      接下来要做的事情：
      <ul className={`${baseClass}__instructions`}>
        <li>
          <SeedButton />
          {' 一些页面、文章和项目来快速启动您的新网站，然后 '}
          <a href="/" target="_blank">
            访问您的网站
          </a>
          {' 查看结果。'}
        </li>
        <li>
          如果您使用 Payload Cloud 创建了此仓库，请前往 GitHub
          并将其克隆到您的本地计算机。它将位于您创建此项目时选择的 <i>GitHub 范围</i> 下。
        </li>
        <li>
          {'根据需要修改您的 '}
          <a
            href="https://payloadcms.com/docs/configuration/collections"
            rel="noopener noreferrer"
            target="_blank"
          >
            集合
          </a>
          {' 并添加更多 '}
          <a
            href="https://payloadcms.com/docs/fields/overview"
            rel="noopener noreferrer"
            target="_blank"
          >
            字段
          </a>
          {'。如果您是 Payload 新手，我们还建议您查看 '}
          <a
            href="https://payloadcms.com/docs/getting-started/what-is-payload"
            rel="noopener noreferrer"
            target="_blank"
          >
            入门指南
          </a>
          {' 文档。'}
        </li>
        <li>提交并推送您的更改到仓库以触发项目的重新部署。</li>
      </ul>
      {'专业提示：这个区块是一个 '}
      <a
        href="https://payloadcms.com/docs/admin/custom-components/overview#base-component-overrides"
        rel="noopener noreferrer"
        target="_blank"
      >
        自定义组件
      </a>
      ，您可以通过更新您的 <strong>payload.config</strong> 随时删除它。
    </div>
  )
}

export default BeforeDashboard
