import type { Block } from 'payload'

import {
  FixedToolbarFeature,
  InlineToolbarFeature,
  lexicalEditor,
} from '@payloadcms/richtext-lexical'

export const Banner: Block = {
  slug: 'banner',
  labels: {
    singular: {
      en: 'Banner',
      zh: '横幅',
    },
    plural: {
      en: 'Banners',
      zh: '横幅',
    },
  },
  fields: [
    {
      name: 'style',
      type: 'select',
      label: {
        en: 'Style',
        zh: '样式',
      },
      defaultValue: 'info',
      options: [
        {
          label: {
            en: 'Info',
            zh: '信息',
          },
          value: 'info',
        },
        {
          label: {
            en: 'Warning',
            zh: '警告',
          },
          value: 'warning',
        },
        {
          label: {
            en: 'Error',
            zh: '错误',
          },
          value: 'error',
        },
        {
          label: {
            en: 'Success',
            zh: '成功',
          },
          value: 'success',
        },
      ],
      required: true,
    },
    {
      name: 'content',
      type: 'richText',
      label: {
        en: 'Content',
        zh: '内容',
      },
      editor: lexicalEditor({
        features: ({ rootFeatures }) => {
          return [...rootFeatures, FixedToolbarFeature(), InlineToolbarFeature()]
        },
      }),
      required: true,
    },
  ],
  interfaceName: 'BannerBlock',
}
