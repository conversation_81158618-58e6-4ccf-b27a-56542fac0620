import type { Block } from 'payload'

import {
  FixedToolbarFeature,
  HeadingFeature,
  InlineToolbarFeature,
  lexicalEditor,
} from '@payloadcms/richtext-lexical'

export const Archive: Block = {
  slug: 'archive',
  interfaceName: 'ArchiveBlock',
  labels: {
    plural: {
      en: 'Archives',
      zh: '归档',
    },
    singular: {
      en: 'Archive',
      zh: '归档',
    },
  },
  fields: [
    {
      name: 'introContent',
      type: 'richText',
      editor: lexicalEditor({
        features: ({ rootFeatures }) => {
          return [
            ...rootFeatures,
            HeadingFeature({ enabledHeadingSizes: ['h1', 'h2', 'h3', 'h4'] }),
            FixedToolbarFeature(),
            InlineToolbarFeature(),
          ]
        },
      }),
      label: {
        en: 'Intro Content',
        zh: '介绍内容',
      },
    },
    {
      name: 'populateBy',
      type: 'select',
      label: {
        en: 'Populate By',
        zh: '填充方式',
      },
      defaultValue: 'collection',
      options: [
        {
          label: {
            en: 'Collection',
            zh: '集合',
          },
          value: 'collection',
        },
        {
          label: {
            en: 'Individual Selection',
            zh: '单独选择',
          },
          value: 'selection',
        },
      ],
    },
    {
      name: 'relationTo',
      type: 'select',
      admin: {
        condition: (_, siblingData) => siblingData.populateBy === 'collection',
      },
      defaultValue: 'posts',
      label: {
        en: 'Collections To Show',
        zh: '要显示的集合',
      },
      options: [
        {
          label: {
            en: 'Posts',
            zh: '文章',
          },
          value: 'posts',
        },
      ],
    },
    {
      name: 'categories',
      type: 'relationship',
      admin: {
        condition: (_, siblingData) => siblingData.populateBy === 'collection',
      },
      hasMany: true,
      label: {
        en: 'Categories To Show',
        zh: '要显示的分类',
      },
      relationTo: 'categories',
    },
    {
      name: 'limit',
      type: 'number',
      admin: {
        condition: (_, siblingData) => siblingData.populateBy === 'collection',
        step: 1,
      },
      defaultValue: 10,
      label: {
        en: 'Limit',
        zh: '限制数量',
      },
    },
    {
      name: 'selectedDocs',
      type: 'relationship',
      admin: {
        condition: (_, siblingData) => siblingData.populateBy === 'selection',
      },
      hasMany: true,
      label: {
        en: 'Selection',
        zh: '选择',
      },
      relationTo: ['posts'],
    },
  ],
}
