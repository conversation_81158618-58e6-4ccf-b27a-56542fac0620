import type { Block } from 'payload'

export const Code: Block = {
  slug: 'code',
  interfaceName: 'CodeBlock',
  labels: {
    singular: {
      en: 'Code Block',
      zh: '代码块',
    },
    plural: {
      en: 'Code Blocks',
      zh: '代码块',
    },
  },
  fields: [
    {
      name: 'language',
      type: 'select',
      label: {
        en: 'Language',
        zh: '编程语言',
      },
      defaultValue: 'typescript',
      options: [
        {
          label: 'TypeScript',
          value: 'typescript',
        },
        {
          label: 'JavaScript',
          value: 'javascript',
        },
        {
          label: 'CSS',
          value: 'css',
        },
      ],
    },
    {
      name: 'code',
      type: 'code',
      label: {
        en: 'Code',
        zh: '代码',
      },
      required: true,
    },
  ],
}
