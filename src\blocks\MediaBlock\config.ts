import type { Block } from 'payload'

export const MediaBlock: Block = {
  slug: 'mediaBlock',
  interfaceName: 'MediaBlock',
  labels: {
    singular: {
      en: 'Media Block',
      zh: '媒体块',
    },
    plural: {
      en: 'Media Blocks',
      zh: '媒体块',
    },
  },
  fields: [
    {
      name: 'media',
      type: 'upload',
      label: {
        en: 'Media',
        zh: '媒体文件',
      },
      relationTo: 'media',
      required: true,
    },
  ],
}
