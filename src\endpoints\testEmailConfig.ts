import type { Endpoint } from 'payload'
import * as nodemailer from 'nodemailer'

export const testEmailConfigEndpoint: Endpoint = {
  path: '/test-email-config',
  method: 'post',
  handler: async (req) => {
    const { payload, user } = req

    // 检查用户权限
    if (!user) {
      return Response.json({ error: '未授权访问' }, { status: 401 })
    }

    try {
      let host,
        port,
        secure,
        username,
        password,
        fromEmail,
        fromName,
        testEmail,
        testSubject,
        testContent

      // 安全地解析请求体
      try {
        const body = await req.json?.()
        if (!body) {
          throw new Error('No request body')
        }
        ;({
          host,
          port,
          secure,
          username,
          password,
          fromEmail,
          fromName,
          testEmail,
          testSubject,
          testContent,
        } = body)
      } catch (_parseError) {
        return Response.json(
          {
            success: false,
            message: '请求数据格式错误',
          },
          { status: 400 },
        )
      }

      // 验证必填字段
      if (!host || !port || !username || !password || !fromEmail || !testEmail) {
        return Response.json(
          {
            success: false,
            message: '缺少必填字段',
          },
          { status: 400 },
        )
      }

      // 创建测试传输器
      const transporter = nodemailer.createTransport({
        host,
        port: Number(port),
        secure: Boolean(secure),
        auth: {
          user: username,
          pass: password,
        },
        tls: {
          rejectUnauthorized: false, // 允许自签名证书
        },
        debug: true, // 启用调试
        logger: true, // 启用日志
      })

      // 验证连接
      await transporter.verify()

      // 发送测试邮件
      const testResult = await transporter.sendMail({
        from: `${fromName || 'Test Email'} <${fromEmail}>`,
        to: testEmail,
        subject: testSubject || '邮件配置测试 - Email Configuration Test',
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
            <h2 style="color: #2c3e50;">🎉 邮件配置测试成功！</h2>
            <p>恭喜！您的邮件配置工作正常。</p>
            
            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
              <h3 style="margin-top: 0; color: #495057;">测试内容：</h3>
              <p style="margin-bottom: 0;">${testContent || '这是一封测试邮件，用于验证邮件配置是否正常工作。'}</p>
            </div>
            
            <hr style="border: none; border-top: 1px solid #dee2e6; margin: 20px 0;">
            
            <h3 style="color: #2c3e50;">📋 配置信息</h3>
            <ul style="list-style: none; padding: 0;">
              <li><strong>SMTP服务器:</strong> ${host}</li>
              <li><strong>端口:</strong> ${port}</li>
              <li><strong>安全连接:</strong> ${secure ? '是' : '否'}</li>
              <li><strong>发件人:</strong> ${fromName || 'Test Email'} &lt;${fromEmail}&gt;</li>
              <li><strong>测试时间:</strong> ${new Date().toLocaleString('zh-CN')}</li>
            </ul>
            
            <div style="margin-top: 30px; padding: 15px; background-color: #d4edda; border-radius: 5px; border-left: 4px solid #28a745;">
              <p style="margin: 0; color: #155724;">
                <strong>✅ 测试成功！</strong> 您的邮件配置已经可以正常使用了。
              </p>
            </div>
          </div>
        `,
        text: `
邮件配置测试成功！

测试内容：
${testContent || '这是一封测试邮件，用于验证邮件配置是否正常工作。'}

配置信息：
- SMTP服务器: ${host}
- 端口: ${port}
- 安全连接: ${secure ? '是' : '否'}
- 发件人: ${fromName || 'Test Email'} <${fromEmail}>
- 测试时间: ${new Date().toLocaleString('zh-CN')}

✅ 测试成功！您的邮件配置已经可以正常使用了。
        `,
      })

      payload.logger.info(`Test email sent successfully to ${testEmail}`)

      return Response.json({
        success: true,
        message: `测试邮件已发送到 ${testEmail}`,
        messageId: testResult.messageId,
      })
    } catch (error) {
      payload.logger.error('Test email configuration error:', error)

      let errorMessage = '邮件配置测试失败'
      const err = error as { code?: string; message?: string } // 类型断言以访问错误属性

      if (err.code === 'EAUTH') {
        errorMessage = '认证失败，请检查用户名和密码'
      } else if (err.code === 'ECONNECTION') {
        errorMessage = '连接失败，请检查SMTP服务器和端口'
      } else if (err.code === 'ESOCKET') {
        errorMessage = '网络连接错误，请检查网络设置'
      } else if (err.message) {
        errorMessage = err.message
      }

      return Response.json(
        {
          success: false,
          message: errorMessage,
          error: err.code || 'UNKNOWN_ERROR',
        },
        { status: 400 },
      )
    }
  },
}
