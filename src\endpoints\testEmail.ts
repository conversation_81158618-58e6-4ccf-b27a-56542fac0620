import type { Endpoint } from 'payload'
import { testEmailConfiguration } from '../services/emailService'

export const testEmailEndpoint: Endpoint = {
  path: '/test-email',
  method: 'post',
  handler: async (req) => {
    const { payload, user } = req

    // 检查用户权限
    if (!user) {
      return Response.json({ error: '未授权访问' }, { status: 401 })
    }

    try {
      let configName: string | undefined

      // 安全地解析请求体
      try {
        const body = await req.json?.()
        configName = body?.configName
      } catch {
        // 如果没有请求体或解析失败，使用默认值
        configName = undefined
      }

      const result = await testEmailConfiguration(payload, configName)

      if (result.success) {
        return Response.json({
          success: true,
          message: result.message,
        })
      } else {
        return Response.json(
          {
            success: false,
            message: result.message,
            error: result.error,
          },
          { status: 400 },
        )
      }
    } catch (error) {
      payload.logger.error('Test email endpoint error:', error)
      return Response.json(
        {
          success: false,
          message: '测试邮件发送失败',
          error: error instanceof Error ? error.message : String(error),
        },
        { status: 500 },
      )
    }
  },
}
