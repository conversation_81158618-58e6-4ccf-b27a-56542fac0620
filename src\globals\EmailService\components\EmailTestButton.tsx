'use client'

import React, { useState } from 'react'
import { Button, toast } from '@payloadcms/ui'

export const EmailTestButton: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false)

  // 从DOM中获取表单字段值
  const getFieldValue = (fieldName: string): string => {
    const input = document.querySelector(
      `input[name*="${fieldName}"], textarea[name*="${fieldName}"]`,
    ) as HTMLInputElement | HTMLTextAreaElement
    return input?.value || ''
  }

  const getCheckboxValue = (fieldName: string): boolean => {
    const checkbox = document.querySelector(
      `input[name*="${fieldName}"][type="checkbox"]`,
    ) as HTMLInputElement
    return checkbox?.checked || false
  }

  const handleTest = async () => {
    // 从DOM获取字段值
    const host = getFieldValue('host')
    const port = getFieldValue('port')
    const secure = getCheckboxValue('secure')
    const username = getFieldValue('username')
    const password = getFieldValue('password')
    const fromEmail = getFieldValue('fromEmail')
    const fromName = getFieldValue('fromName')
    const testEmail = getFieldValue('testEmail')
    const testSubject = getFieldValue('testSubject')
    const testContent = getFieldValue('testContent')

    // 调试：打印字段值
    console.log('Debug field values from DOM:', {
      host,
      port,
      secure,
      username,
      password,
      fromEmail,
      fromName,
      testEmail,
      testSubject,
      testContent,
    })

    if (!host || !port || !username || !password || !fromEmail || !testEmail) {
      toast.error(
        `请填写所有必填字段后再测试。缺失字段: ${[
          !host && 'SMTP服务器',
          !port && '端口',
          !username && '用户名',
          !password && '密码',
          !fromEmail && '发件邮箱',
          !testEmail && '测试邮箱',
        ]
          .filter(Boolean)
          .join(', ')}`,
      )
      return
    }

    setIsLoading(true)

    try {
      const testConfig = {
        host: host,
        port: Number(port),
        secure: Boolean(secure),
        username: username,
        password: password,
        fromEmail: fromEmail,
        fromName: fromName || 'Test Email',
        testEmail: testEmail,
        testSubject: testSubject || '邮件配置测试',
        testContent: testContent || '这是一封测试邮件，用于验证邮件配置是否正常工作。',
      }

      const response = await fetch('/api/test-email-config', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(testConfig),
      })

      const result = await response.json()

      if (result.success) {
        toast.success(`测试邮件发送成功！已发送到 ${testEmail}`)
      } else {
        toast.error(`测试失败：${result.message}`)
      }
    } catch (error) {
      console.error('Test email error:', error)
      toast.error('测试邮件发送失败，请检查网络连接')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div style={{ marginTop: '1rem', marginBottom: '1rem' }}>
      <Button onClick={handleTest} disabled={isLoading} size="small" buttonStyle="primary">
        {isLoading ? '发送中...' : '测试邮件配置'}
      </Button>
      <p
        style={{
          fontSize: '0.875rem',
          color: '#666',
          marginTop: '0.5rem',
          marginBottom: 0,
        }}
      >
        点击测试按钮将使用当前填写的配置发送测试邮件到指定地址
      </p>
    </div>
  )
}
